@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tajawal font is loaded from Google Fonts in index.html */

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(0, 0%, 8.6%);
  --muted: hsl(0, 0%, 87.8%);
  --muted-foreground: hsl(0, 0%, 43.5%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 8.6%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 8.6%);
  --border: hsl(0, 0%, 87.8%);
  --input: hsl(0, 0%, 87.8%);
  --primary: hsl(218, 100%, 53%);
  --primary-foreground: hsl(220, 82%, 98%);
  --secondary: hsl(0, 0%, 95.9%);
  --secondary-foreground: hsl(0, 0%, 8.6%);
  --accent: hsl(218, 100%, 88%);
  --accent-foreground: hsl(0, 0%, 8.6%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(0, 0%, 8.6%);
  --radius: 0.5rem;
  
  /* IBM Carbon Colors - Extended Palette */
  --ibm-blue-100: hsl(220, 68%, 18%);
  --ibm-blue-90: hsl(220, 68%, 22%);
  --ibm-blue-80: hsl(220, 69%, 26%);
  --ibm-blue-70: hsl(220, 69%, 32%);
  --ibm-blue-60: hsl(218, 100%, 53%);
  --ibm-blue-50: hsl(218, 100%, 62%);
  --ibm-blue-40: hsl(218, 100%, 70%);
  --ibm-blue-30: hsl(218, 100%, 78%);
  --ibm-blue-20: hsl(218, 100%, 88%);
  --ibm-blue-10: hsl(218, 100%, 95%);

  --ibm-gray-100: hsl(0, 0%, 8.6%);
  --ibm-gray-90: hsl(0, 0%, 15.7%);
  --ibm-gray-80: hsl(0, 0%, 22.7%);
  --ibm-gray-70: hsl(0, 0%, 29.8%);
  --ibm-gray-60: hsl(0, 0%, 43.5%);
  --ibm-gray-50: hsl(0, 0%, 56.9%);
  --ibm-gray-40: hsl(0, 0%, 69.8%);
  --ibm-gray-30: hsl(0, 0%, 78.4%);
  --ibm-gray-20: hsl(0, 0%, 87.8%);
  --ibm-gray-10: hsl(0, 0%, 96.1%);

  --ibm-white: hsl(0, 0%, 100%);
  --ibm-black: hsl(0, 0%, 0%);
}

.dark {
  --background: var(--ibm-gray-100);
  --foreground: var(--ibm-gray-10);
  --muted: var(--ibm-gray-90);
  --muted-foreground: var(--ibm-gray-40);
  --popover: var(--ibm-gray-90);
  --popover-foreground: var(--ibm-gray-10);
  --card: var(--ibm-gray-90);
  --card-foreground: var(--ibm-gray-10);
  --border: var(--ibm-gray-80);
  --input: var(--ibm-gray-80);
  --primary: var(--ibm-blue-60);
  --primary-foreground: var(--ibm-white);
  --secondary: var(--ibm-gray-80);
  --secondary-foreground: var(--ibm-gray-10);
  --accent: var(--ibm-gray-80);
  --accent-foreground: var(--ibm-gray-10);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: var(--ibm-white);
  --ring: var(--ibm-gray-40);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .tajawal-light {
    font-family: 'Tajawal', sans-serif;
    font-weight: 400;
  }

  .tajawal-regular {
    font-family: 'Tajawal', sans-serif;
    font-weight: 500;
  }

  .tajawal-medium {
    font-family: 'Tajawal', sans-serif;
    font-weight: 600;
  }

  .tajawal-bold {
    font-family: 'Tajawal', sans-serif;
    font-weight: 700;
  }

  .tajawal-heavy {
    font-family: 'Tajawal', sans-serif;
    font-weight: 800;
  }

  .tajawal-black {
    font-family: 'Tajawal', sans-serif;
    font-weight: 900;
  }

  .project-card {
    transition: all 0.3s ease;
  }

  .project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .hero-bg {
    background: linear-gradient(135deg, var(--ibm-blue-60) 0%, var(--ibm-blue-80) 100%);
  }

  .counter-animate {
    animation: countUp 2s ease-out;
  }

  @keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
