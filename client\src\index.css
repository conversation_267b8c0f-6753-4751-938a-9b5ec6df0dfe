@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'HTHeliopolis';
  src: url('data:font/truetype;charset=utf-8;base64,') format('truetype');
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: 'HTHeliopolis';
  src: url('data:font/truetype;charset=utf-8;base64,') format('truetype');
  font-weight: 800;
  font-display: swap;
}

@font-face {
  font-family: 'HTHeliopolis';
  src: url('data:font/truetype;charset=utf-8;base64,') format('truetype');
  font-weight: 900;
  font-display: swap;
}

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(0, 0%, 8.6%);
  --muted: hsl(0, 0%, 87.8%);
  --muted-foreground: hsl(0, 0%, 43.5%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 8.6%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 8.6%);
  --border: hsl(0, 0%, 87.8%);
  --input: hsl(0, 0%, 87.8%);
  --primary: hsl(218, 100%, 53%);
  --primary-foreground: hsl(220, 82%, 98%);
  --secondary: hsl(0, 0%, 95.9%);
  --secondary-foreground: hsl(0, 0%, 8.6%);
  --accent: hsl(218, 100%, 88%);
  --accent-foreground: hsl(0, 0%, 8.6%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(0, 0%, 8.6%);
  --radius: 0.5rem;
  
  /* IBM Carbon Colors */
  --ibm-blue-60: hsl(218, 100%, 53%);
  --ibm-blue-50: hsl(218, 100%, 62%);
  --ibm-blue-20: hsl(218, 100%, 88%);
  --ibm-gray-100: hsl(0, 0%, 8.6%);
  --ibm-gray-60: hsl(0, 0%, 43.5%);
  --ibm-gray-20: hsl(0, 0%, 87.8%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(218, 100%, 53%);
  --primary-foreground: hsl(220, 82%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'HTHeliopolis', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .hthelios-bold {
    font-family: 'HTHeliopolis', sans-serif;
    font-weight: 700;
  }

  .hthelios-heavy {
    font-family: 'HTHeliopolis', sans-serif;
    font-weight: 900;
  }

  .hthelios-black {
    font-family: 'HTHeliopolis', sans-serif;
    font-weight: 800;
  }

  .project-card {
    transition: all 0.3s ease;
  }

  .project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .hero-bg {
    background: linear-gradient(135deg, var(--ibm-blue-60) 0%, var(--ibm-blue-50) 100%);
  }

  .counter-animate {
    animation: countUp 2s ease-out;
  }

  @keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
